{"fields": [{"type": "select", "name": "productCategoryId", "label": "Product Category", "placeholder": "Select product category", "icon": "pi-tags", "validation": {"required": true}, "options": []}, {"type": "custom", "name": "productSelection", "label": "Product Selection", "placeholder": "Search and select products", "icon": "pi-shopping-cart", "validation": {"required": true}, "customComponent": "ProductSelection"}, {"type": "date", "name": "expectedDeliveryDate", "label": "Expected Arrival Date", "placeholder": "Select expected delivery date", "icon": "pi-calendar", "validation": {"required": true}, "dateRestriction": {"range": "future", "includeToday": true}}, {"type": "select", "name": "procurementSource", "label": "Procurement Source", "placeholder": "Select procurement source", "icon": "pi-truck", "validation": {"required": true}, "options": [{"label": "Request for Quotation (RFQ)", "value": "RFQ"}, {"label": "Online Purchase", "value": "ONLINE"}, {"label": "Direct Vendor", "value": "DIRECT_VENDOR"}, {"label": "Direct Shop", "value": "DIRECT_SHOP"}]}, {"type": "select", "name": "requestType", "label": "Request Type", "placeholder": "Select request type", "icon": "pi-shopping-bag", "validation": {"required": true}, "options": [{"label": "Buy New", "value": "BUY"}, {"label": "Buy Used/Old", "value": "BUY_OLD"}, {"label": "Rent", "value": "RENT"}]}, {"type": "textarea", "name": "description", "label": "Comments", "placeholder": "Enter additional comments or notes (optional)", "icon": "pi-comment", "validation": {"required": false, "maxLength": 500}, "rows": 5}], "actions": [{"id": "submit", "type": "submit", "label": "Create Purchase Request"}, {"id": "cancel", "type": "button", "label": "Cancel"}]}