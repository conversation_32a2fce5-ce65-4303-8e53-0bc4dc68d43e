/* Create Purchase Request Page Styles */
.create-purchase-request {
  min-height: 100vh;
}

.create-purchase-request .p-card {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.create-purchase-request .p-card-title {
  color: var(--primary-color);
  font-weight: 600;
}

.create-purchase-request .p-card-subtitle {
  color: var(--text-color-secondary);
  margin-top: 0.5rem;
}

/* Product Selection Styles */
.product-selection {
  width: 100%;
}

.product-selection .p-autocomplete {
  width: 100%;
}

.product-selection .p-autocomplete .p-autocomplete-input {
  width: 100%;
}

.product-selection .selected-products {
  margin-top: 1rem;
}

.product-selection .selected-products .p-card {
  border: 1px solid var(--surface-border);
  background: var(--surface-ground);
  padding: 1rem;
  transition: all 0.2s ease;
}

.product-selection .selected-products .p-card:hover {
  border-color: var(--primary-color);
  background: var(--surface-hover);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.product-selection .p-inputnumber {
  width: 100px !important;
  min-width: 100px;
}

.product-selection .p-inputnumber .p-inputnumber-input {
  text-align: center;
  width: 100% !important;
}

.product-selection .p-inputnumber .p-inputnumber-button-group {
  display: flex;
}

.product-selection .p-inputnumber .p-inputnumber-button {
  width: 1.5rem;
  height: 1.5rem;
}

/* Product card layout */
.product-selection .selected-products .p-card .flex {
  align-items: center;
  gap: 1rem;
}

.product-selection .selected-products .p-card .flex > div:first-child {
  flex: 1;
  min-width: 0; /* Allow text truncation */
}

.product-selection .selected-products .p-card .flex > div:last-child {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* Button styling */
.product-selection .p-button.p-button-sm {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
}

.product-selection .p-button[data-pc-name="button"][data-pc-section="root"] {
  min-width: auto;
}

/* Empty state styles */
.product-selection .border-dashed {
  border-style: dashed !important;
  border-width: 2px !important;
  border-color: var(--surface-border) !important;
}

.product-selection .border-round-md {
  border-radius: 0.5rem;
}

.product-selection .text-400 {
  color: var(--text-color-secondary);
}

.product-selection .text-500 {
  color: var(--text-color-secondary);
}

.product-selection .text-600 {
  color: var(--text-color-secondary);
}

.product-selection .text-900 {
  color: var(--text-color);
}

/* Form field spacing */
.create-purchase-request .form-field-wrapper {
  margin-bottom: 1.5rem;
}

.create-purchase-request .form-field-wrapper:last-child {
  margin-bottom: 0;
}

/* Button styles */
.create-purchase-request .form-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid var(--surface-border);
}

.create-purchase-request .form-actions .p-button {
  min-width: 120px;
}

/* Responsive design */
@media (max-width: 768px) {
  .create-purchase-request {
    padding: 1rem;
  }

  .create-purchase-request .max-w-4xl {
    max-width: 100%;
  }

  .create-purchase-request .form-actions {
    flex-direction: column;
  }

  .create-purchase-request .form-actions .p-button {
    width: 100%;
  }

  /* Mobile product selection layout */
  .product-selection .selected-products .p-card .flex {
    flex-direction: column;
    align-items: flex-start !important;
    gap: 1rem;
  }

  .product-selection .selected-products .p-card .flex > div:first-child {
    width: 100%;
  }

  .product-selection .selected-products .p-card .flex > div:last-child {
    width: 100%;
    justify-content: space-between;
    flex-direction: row;
    align-items: center;
  }

  .product-selection .p-inputnumber {
    width: 80px !important;
    min-width: 80px;
  }
}

@media (max-width: 480px) {
  .product-selection .selected-products .p-card .flex > div:last-child {
    flex-direction: column;
    gap: 0.75rem;
    align-items: stretch;
  }

  .product-selection .selected-products .p-card .flex > div:last-child > div {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}

/* Loading state */
.create-purchase-request .p-button.p-button-loading {
  pointer-events: none;
}

/* Error state */
.create-purchase-request .p-invalid {
  border-color: var(--red-500) !important;
}

.create-purchase-request .p-error {
  color: var(--red-500);
  font-size: 0.875rem;
  margin-top: 0.25rem;
}
