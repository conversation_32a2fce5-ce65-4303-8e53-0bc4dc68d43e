import { useState, useEffect, useCallback, useMemo } from 'react';
import { Controller, useFormContext } from 'react-hook-form';
import { AutoComplete } from 'primereact/autocomplete';
import { InputNumber } from 'primereact/inputnumber';
import { Button } from 'primereact/button';
import { Card } from 'primereact/card';
import { useOrganizationContext } from '@/context/OrganizationContext';
import {
  useSearchProductsByCategory,
  useSearchOrganizationProductsByCategory
} from '@/hooks/usePurchaseRequest';
import {
  ProductSelectionItem
} from '@/types/catalog.types';

interface ProductSelectionProps {
  name: string;
  label?: string;
  placeholder?: string;
  icon?: string;
  categoryId?: number;
}

interface ProductOption {
  label: string;
  value: string;
  type: 'product' | 'organizationProduct';
  productId?: number;
  organizationProductId?: number;
  productName: string;
}

export const ProductSelection = ({
  name,
  label,
  placeholder,
  icon,
  categoryId
}: ProductSelectionProps) => {
  const {
    control,
    formState: { errors },
    watch
  } = useFormContext();

  const { organizationId } = useOrganizationContext();
  const [searchQuery, setSearchQuery] = useState('');

  // Watch for category changes
  const watchedCategoryId = watch('productCategoryId');
  const effectiveCategoryId = categoryId || (watchedCategoryId ? parseInt(watchedCategoryId) : null);

  const error = errors[name]?.message as string | undefined;

  // Search for products and organization products
  const { data: products = [] } = useSearchProductsByCategory(
    effectiveCategoryId,
    searchQuery,
    organizationId
  );

  const { data: orgProducts = [] } = useSearchOrganizationProductsByCategory(
    organizationId || 0,
    effectiveCategoryId,
    searchQuery
  );



  // Compute suggestions directly from API data (no useState needed)
  const suggestions = useMemo((): ProductOption[] => {
    if (!effectiveCategoryId) {
      return [];
    }

    const productOptions: ProductOption[] = products.map(product => ({
      label: `${product.name} (Global Product)`,
      value: `product_${product.id}`,
      type: 'product' as const,
      productId: product.id,
      productName: product.name
    }));

    const orgProductOptions: ProductOption[] = orgProducts.map(product => ({
      label: `${product.name} (Organization Product)`,
      value: `org_${product.id}`,
      type: 'organizationProduct' as const,
      organizationProductId: product.id,
      productName: product.name
    }));

    return [...productOptions, ...orgProductOptions];
  }, [products, orgProducts, effectiveCategoryId]);

  // Handle search input
  const handleSearch = useCallback((event: { query: string }) => {
    setSearchQuery(event.query);
  }, []);

  // Clear search when category changes
  useEffect(() => {
    setSearchQuery('');
  }, [effectiveCategoryId]);

  // Add a new product selection
  const addProduct = useCallback((selectedOption: ProductOption, currentProducts: ProductSelectionItem[]) => {
    // Check if product is already selected
    const isAlreadySelected = currentProducts.some(p =>
      (p.type === 'product' && p.productId === selectedOption.productId) ||
      (p.type === 'organizationProduct' && p.organizationProductId === selectedOption.organizationProductId)
    );

    if (isAlreadySelected) {
      return currentProducts; // Don't add duplicate
    }

    const newProduct: ProductSelectionItem = {
      id: `${Date.now()}_${Math.random()}`,
      type: selectedOption.type,
      productId: selectedOption.productId,
      organizationProductId: selectedOption.organizationProductId,
      productName: selectedOption.productName,
      quantity: 1
    };

    return [...currentProducts, newProduct];
  }, []);

  // Remove a product selection
  const removeProduct = useCallback((productId: string, currentProducts: ProductSelectionItem[]) => {
    return currentProducts.filter(p => p.id !== productId);
  }, []);

  // Update product quantity
  const updateQuantity = useCallback((productId: string, quantity: number, currentProducts: ProductSelectionItem[]) => {
    return currentProducts.map(p =>
      p.id === productId ? { ...p, quantity: quantity || 1 } : p
    );
  }, []);

  // Custom item template for dropdown
  const itemTemplate = (item: ProductOption) => {
    return (
      <div className="flex align-items-center">
        <i className={`pi ${item.type === 'product' ? 'pi-box' : 'pi-building'} mr-2`}></i>
        <div>
          <div className="font-medium">{item.productName}</div>
          <div className="text-sm text-color-secondary">
            {item.type === 'product' ? 'Global Product' : 'Organization Product'}
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="field mb-3">
      {label && <label htmlFor={name} className="block mb-1">{label}</label>}
      <Controller
        name={name}
        control={control}
        render={({ field }) => {
          const currentProducts: ProductSelectionItem[] = field.value || [];

          return (
            <div className={`product-selection ${error ? 'p-invalid' : ''}`}>
              {/* Product Search */}
              <div className="mb-2">
                <div className={`p-inputgroup ${error ? 'p-invalid' : ''}`}>
                  {icon && <span className="p-inputgroup-addon"><i className={`pi ${icon}`}></i></span>}
                  <AutoComplete
                    value={searchQuery}
                    suggestions={suggestions}
                    completeMethod={handleSearch}
                    onChange={(e) => setSearchQuery(typeof e.value === 'string' ? e.value : '')}
                    onSelect={(e) => {
                      if (e.value && typeof e.value === 'object') {
                        const updatedProducts = addProduct(e.value, currentProducts);
                        field.onChange(updatedProducts);
                        setSearchQuery(''); // Clear search after selection
                      }
                    }}
                    placeholder={placeholder || 'Search for products...'}
                    itemTemplate={itemTemplate}
                    className="w-full"
                    disabled={!effectiveCategoryId}
                    emptyMessage={!effectiveCategoryId ? 'Please select a category first' : 'No products found'}
                  />
                </div>
                {!effectiveCategoryId && (
                  <small className="text-color-secondary mt-1 block">
                    Please select a product category first to search for products.
                  </small>
                )}
              </div>

              {/* Selected Products */}
              {currentProducts.length > 0 && (
                <div className="selected-products">
                  <h6 className="mb-2 text-sm font-medium text-700">Selected Products:</h6>
                  <div className="product-selection-list">
                    {currentProducts.map((product) => (
                      <div key={product.id} className="product-selection-item">
                        <div className="flex align-items-center justify-content-between p-2 border-1 surface-border border-round mb-1">
                          <div className="flex align-items-center flex-1 min-w-0">
                            <i className={`pi ${product.type === 'product' ? 'pi-box' : 'pi-building'} mr-2 text-primary flex-shrink-0`} style={{ fontSize: '0.875rem' }}></i>
                            <div className="flex-1 min-w-0">
                              <div className="font-medium text-900 text-sm truncate">{product.productName}</div>
                              <div className="text-xs text-600">
                                {product.type === 'product' ? 'Global Product' : 'Organization Product'}
                              </div>
                            </div>
                          </div>
                          <div className="flex align-items-center gap-2 flex-shrink-0">
                            <span className="text-xs font-medium text-700 hidden sm:inline">Qty:</span>
                            <InputNumber
                              value={product.quantity}
                              onValueChange={(e) => {
                                const updatedProducts = updateQuantity(product.id, e.value || 1, currentProducts);
                                field.onChange(updatedProducts);
                              }}
                              min={1}
                              max={9999}
                              size={3}
                              showButtons
                              buttonLayout="horizontal"
                              className="product-quantity-input"
                              style={{ width: '90px' }}
                            />
                            <Button
                              icon="pi pi-trash"
                              severity="danger"
                              text
                              size="small"
                              onClick={() => {
                                const updatedProducts = removeProduct(product.id, currentProducts);
                                field.onChange(updatedProducts);
                              }}
                              tooltip="Remove product"
                              className="p-1"
                              style={{ minWidth: '2rem', height: '2rem' }}
                            />
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {currentProducts.length === 0 && (
                <div className="text-center p-3 border-1 border-dashed surface-border border-round-md">
                  <i className="pi pi-shopping-cart text-3xl text-400 mb-2 block"></i>
                  <p className="text-600 m-0 mb-1 text-sm">No products selected</p>
                  <small className="text-500 text-xs">
                    Search and select products from the dropdown above
                  </small>
                </div>
              )}
            </div>
          );
        }}
      />
      {error && <small className="p-error">{error}</small>}
    </div>
  );
};
