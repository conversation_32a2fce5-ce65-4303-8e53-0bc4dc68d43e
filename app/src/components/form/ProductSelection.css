@import url('../../variables.css');

/* Product Selection Component Styles */
.product-selection {
  width: 100%;
}

/* Selected Products List */
.product-selection-list {
  max-height: 300px;
  overflow-y: auto;
}

/* Individual Product Selection Item */
.product-selection-item {
  transition: all 0.2s ease;
}

.product-selection-item:hover .border-1 {
  border-color: var(--color-primary) !important;
  background-color: var(--surface-hover);
}

/* Compact Product Item Layout */
.product-selection-item .border-1 {
  background-color: var(--surface-card);
  transition: all 0.2s ease;
  min-height: 3rem;
}

/* Quantity Input Styling */
.product-quantity-input {
  font-size: 0.875rem;
}

/* Style the InputNumber buttons with primary color */
.product-quantity-input .p-inputnumber-buttons-horizontal .p-inputnumber-button {
  background-color: var(--color-primary) !important;
  border-color: var(--color-primary) !important;
  color: white !important;
  width: 1.75rem !important;
  height: 1.75rem !important;
  min-width: 1.75rem !important;
  padding: 0 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.product-quantity-input .p-inputnumber-buttons-horizontal .p-inputnumber-button:hover {
  background-color: var(--color-primary-dark) !important;
  border-color: var(--color-primary-dark) !important;
}

.product-quantity-input .p-inputnumber-buttons-horizontal .p-inputnumber-button:focus {
  box-shadow: 0 0 0 2px var(--color-primary-light) !important;
}

/* Input field styling */
.product-quantity-input .p-inputnumber-input {
  text-align: center !important;
  font-size: 0.875rem !important;
  height: 1.75rem !important;
  padding: 0.25rem 0.5rem !important;
  border-color: var(--surface-border) !important;
}

.product-quantity-input .p-inputnumber-input:focus {
  border-color: var(--color-primary) !important;
  box-shadow: 0 0 0 2px var(--color-primary-light) !important;
}

/* Button group layout */
.product-quantity-input .p-inputnumber-buttons-horizontal {
  display: flex !important;
  align-items: center !important;
}

/* Ensure proper button sizing and alignment */
.product-quantity-input .p-inputnumber-button .p-button-icon {
  font-size: 0.75rem !important;
}

/* Remove button styling */
.product-selection-item .p-button.p-button-text.p-button-danger {
  color: var(--color-danger) !important;
  padding: 0.25rem !important;
}

.product-selection-item .p-button.p-button-text.p-button-danger:hover {
  background-color: var(--color-danger-light) !important;
  color: white !important;
}

/* Icon styling */
.product-selection-item .pi {
  color: var(--color-primary);
}

/* Text truncation for long product names */
.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Empty state styling */
.product-selection .border-dashed {
  border-color: var(--surface-border) !important;
  background-color: var(--surface-ground) !important;
}

/* Responsive adjustments */
@media screen and (max-width: 576px) {
  .product-selection-item .flex.align-items-center.justify-content-between {
    flex-direction: column;
    align-items: stretch;
    gap: 0.75rem;
  }
  
  .product-selection-item .flex.align-items-center.gap-2 {
    justify-content: space-between;
    width: 100%;
  }
  
  .product-quantity-input {
    width: 120px !important;
  }
}

/* Scrollbar styling for product list */
.product-selection-list::-webkit-scrollbar {
  width: 6px;
}

.product-selection-list::-webkit-scrollbar-track {
  background: var(--surface-ground);
  border-radius: 3px;
}

.product-selection-list::-webkit-scrollbar-thumb {
  background: var(--surface-border);
  border-radius: 3px;
}

.product-selection-list::-webkit-scrollbar-thumb:hover {
  background: var(--color-primary);
}

/* Focus states for accessibility */
.product-selection-item .border-1:focus-within {
  border-color: var(--color-primary) !important;
  box-shadow: 0 0 0 2px var(--color-primary-light) !important;
}

/* Compact spacing adjustments */
.product-selection .selected-products h6 {
  font-size: 0.875rem;
  font-weight: 500;
  margin-bottom: 0.5rem;
  color: var(--text-primary);
}

/* Animation for adding/removing items */
.product-selection-item {
  animation: slideIn 0.2s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
