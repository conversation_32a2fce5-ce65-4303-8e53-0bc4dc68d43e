/**
 * Custom hooks for purchase request management operations
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { 
  createPurchaseRequest,
  getPurchaseRequestById,
  getPurchaseRequests,
  getPurchaseRequestsByUserId,
  deletePurchaseRequest,
  approvePurchaseRequest,
  rejectPurchaseRequest,
  searchProductsByCategory,
  searchOrganizationProductsByCategory
} from '@/services/api/purchaseRequestService';
import { 
  CreatePurchaseRequestRequest,
  Product,
  OrganizationProduct
} from '@/types/catalog.types';
import { QueryParams } from '@/types/api/common';

/**
 * Hook for fetching all purchase requests for an organization
 * @param organizationId Organization ID
 * @param params Query parameters for pagination, sorting, and filtering
 * @returns Query result with purchase requests data, loading state, and error state
 */
export const usePurchaseRequests = (organizationId: number, params?: QueryParams) => {
  return useQuery({
    queryKey: ['purchaseRequests', organizationId, params],
    queryFn: () => getPurchaseRequests(organizationId, params),
    enabled: !!organizationId, // Only run the query if organizationId is provided
  });
};

/**
 * Hook for fetching a single purchase request by ID
 * @param organizationId Organization ID
 * @param id Purchase request ID
 * @returns Query result with purchase request data, loading state, and error state
 */
export const usePurchaseRequest = (organizationId: number, id: number) => {
  return useQuery({
    queryKey: ['purchaseRequest', organizationId, id],
    queryFn: () => getPurchaseRequestById(organizationId, id),
    enabled: !!organizationId && !!id, // Only run the query if both IDs are provided
  });
};

/**
 * Hook for fetching purchase requests by user ID
 * @param organizationId Organization ID
 * @param userId User ID
 * @returns Query result with purchase requests data, loading state, and error state
 */
export const usePurchaseRequestsByUser = (organizationId: number, userId: number) => {
  return useQuery({
    queryKey: ['purchaseRequestsByUser', organizationId, userId],
    queryFn: () => getPurchaseRequestsByUserId(organizationId, userId),
    enabled: !!organizationId && !!userId, // Only run the query if both IDs are provided
  });
};

/**
 * Hook for creating a new purchase request
 * @returns Mutation object with mutate function, loading state, and error state
 */
export const useCreatePurchaseRequest = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ organizationId, purchaseRequest }: { 
      organizationId: number; 
      purchaseRequest: CreatePurchaseRequestRequest 
    }) => createPurchaseRequest(organizationId, purchaseRequest),
    onSuccess: (data, variables) => {
      // Invalidate and refetch purchase requests
      queryClient.invalidateQueries({ 
        queryKey: ['purchaseRequests', variables.organizationId] 
      });
      queryClient.invalidateQueries({ 
        queryKey: ['purchaseRequestsByUser', variables.organizationId, variables.purchaseRequest.requestedById] 
      });
    },
  });
};

/**
 * Hook for deleting a purchase request
 * @returns Mutation object with mutate function, loading state, and error state
 */
export const useDeletePurchaseRequest = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ organizationId, id }: { organizationId: number; id: number }) => 
      deletePurchaseRequest(organizationId, id),
    onSuccess: (data, variables) => {
      // Invalidate and refetch purchase requests
      queryClient.invalidateQueries({ 
        queryKey: ['purchaseRequests', variables.organizationId] 
      });
      queryClient.invalidateQueries({ 
        queryKey: ['purchaseRequest', variables.organizationId, variables.id] 
      });
    },
  });
};

/**
 * Hook for approving a purchase request
 * @returns Mutation object with mutate function, loading state, and error state
 */
export const useApprovePurchaseRequest = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ organizationId, id }: { organizationId: number; id: number }) => 
      approvePurchaseRequest(organizationId, id),
    onSuccess: (data, variables) => {
      // Invalidate and refetch purchase requests
      queryClient.invalidateQueries({ 
        queryKey: ['purchaseRequests', variables.organizationId] 
      });
      queryClient.invalidateQueries({ 
        queryKey: ['purchaseRequest', variables.organizationId, variables.id] 
      });
    },
  });
};

/**
 * Hook for rejecting a purchase request
 * @returns Mutation object with mutate function, loading state, and error state
 */
export const useRejectPurchaseRequest = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ organizationId, id }: { organizationId: number; id: number }) => 
      rejectPurchaseRequest(organizationId, id),
    onSuccess: (data, variables) => {
      // Invalidate and refetch purchase requests
      queryClient.invalidateQueries({ 
        queryKey: ['purchaseRequests', variables.organizationId] 
      });
      queryClient.invalidateQueries({ 
        queryKey: ['purchaseRequest', variables.organizationId, variables.id] 
      });
    },
  });
};

/**
 * Hook for searching products by category
 * @param categoryId Category ID
 * @param query Search query
 * @param organizationId Optional organization ID for organization-specific products
 * @returns Query result with products data, loading state, and error state
 */
export const useSearchProductsByCategory = (
  categoryId: number | null,
  query: string,
  organizationId?: number
) => {
  return useQuery({
    queryKey: ['searchProductsByCategory', categoryId, query, organizationId],
    queryFn: () => searchProductsByCategory(categoryId!, query, organizationId),
    enabled: !!categoryId, // Only need category to be selected, query can be empty
    staleTime: 30000, // Cache results for 30 seconds
  });
};

/**
 * Hook for searching organization products by category
 * @param organizationId Organization ID
 * @param categoryId Category ID
 * @param query Search query
 * @returns Query result with organization products data, loading state, and error state
 */
export const useSearchOrganizationProductsByCategory = (
  organizationId: number,
  categoryId: number | null,
  query: string
) => {
  return useQuery({
    queryKey: ['searchOrganizationProductsByCategory', organizationId, categoryId, query],
    queryFn: () => searchOrganizationProductsByCategory(organizationId, categoryId!, query),
    enabled: !!organizationId && !!categoryId, // Only need organizationId and categoryId, query can be empty
    staleTime: 30000, // Cache results for 30 seconds
  });
};

/**
 * Hook for getting products by category (without search)
 * @param categoryId Category ID
 * @param organizationId Optional organization ID
 * @returns Query result with products data, loading state, and error state
 */
export const useProductsByCategory = (categoryId: number | null, organizationId?: number) => {
  return useQuery({
    queryKey: ['productsByCategory', categoryId, organizationId],
    queryFn: () => searchProductsByCategory(categoryId!, '', organizationId),
    enabled: !!categoryId, // Only run if category is selected
    staleTime: 300000, // Cache results for 5 minutes
  });
};

/**
 * Hook for getting organization products by category (without search)
 * @param organizationId Organization ID
 * @param categoryId Category ID
 * @returns Query result with organization products data, loading state, and error state
 */
export const useOrganizationProductsByCategory = (
  organizationId: number,
  categoryId: number | null
) => {
  return useQuery({
    queryKey: ['organizationProductsByCategory', organizationId, categoryId],
    queryFn: () => searchOrganizationProductsByCategory(organizationId, categoryId!, ''),
    enabled: !!organizationId && !!categoryId, // Only run if both IDs are provided
    staleTime: 300000, // Cache results for 5 minutes
  });
};
